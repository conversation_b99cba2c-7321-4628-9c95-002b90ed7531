@echo off
echo ========================================
echo Agricultural Chatbot Setup Script
echo ========================================
echo.

echo Setting up Backend...
cd backend

echo Creating Python virtual environment...
python -m venv venv

echo Activating virtual environment...
call venv\Scripts\activate

echo Installing Python dependencies...
pip install -r requirements.txt

echo Creating environment file...
if not exist .env (
    copy .env.example .env
    echo Please edit backend\.env file with your Perplexity API key
) else (
    echo .env file already exists
)

echo.
echo Setting up Frontend...
cd ..\client

echo Installing Node.js dependencies...
npm install

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Edit backend\.env file with your Perplexity API key
echo 2. Start backend: cd backend && python run.py
echo 3. Start frontend: cd client && npm run dev
echo.
echo Backend will run on: http://localhost:8000
echo Frontend will run on: http://localhost:5173
echo.
pause

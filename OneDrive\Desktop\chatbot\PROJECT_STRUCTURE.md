# 📁 Agricultural Chatbot Project Structure

```
chatbot/
├── 📄 README.md                    # Main project documentation
├── 📄 PROJECT_STRUCTURE.md         # This file - project structure overview
├── 🔧 setup.bat                    # Windows setup script
│
├── 🖥️ backend/                     # Python FastAPI Backend
│   ├── 📄 requirements.txt         # Python dependencies
│   ├── 📄 .env.example            # Environment variables template
│   ├── 📄 .env                    # Environment variables (create from .example)
│   ├── 📄 run.py                  # Backend startup script
│   ├── 📄 test_api.py             # API testing script
│   │
│   └── 📁 app/                    # Main application package
│       ├── 📄 __init__.py
│       ├── 📄 main.py             # FastAPI application entry point
│       ├── 📄 config.py           # Configuration settings
│       ├── 📄 models.py           # Pydantic data models
│       │
│       ├── 📁 services/           # Business logic services
│       │   ├── 📄 __init__.py
│       │   ├── 📄 perplexity_service.py    # Perplexity AI integration
│       │   └── 📄 websocket_manager.py     # WebSocket connection management
│       │
│       └── 📁 routers/            # API route handlers
│           ├── 📄 __init__.py
│           └── 📄 agriculture.py   # Agricultural-specific endpoints
│
└── 🌐 client/                     # React Frontend
    ├── 📄 package.json            # Node.js dependencies
    ├── 📄 package-lock.json       # Dependency lock file
    ├── 📄 vite.config.js          # Vite build configuration
    ├── 📄 tailwind.config.js      # Tailwind CSS configuration
    ├── 📄 eslint.config.js        # ESLint configuration
    ├── 📄 index.html              # HTML entry point
    │
    ├── 📁 public/                 # Static assets
    │
    └── 📁 src/                    # React source code
        ├── 📄 main.jsx            # React application entry point
        ├── 📄 App.jsx             # Main App component
        ├── 📄 App.css             # Custom styles
        ├── 📄 index.css           # Tailwind CSS imports
        │
        ├── 📁 components/         # React components
        │   ├── 📄 ChatInterface.jsx      # Main chat interface
        │   ├── 📄 MessageBubble.jsx      # Individual message component
        │   └── 📄 QuickActions.jsx       # Quick action buttons
        │
        ├── 📁 services/           # API and service integrations
        │   └── 📄 chatService.js         # Chat API service
        │
        └── 📁 assets/             # Static assets (images, icons)
            └── 📄 react.svg
```

## 🔧 Key Components

### Backend Components

#### 🚀 **main.py**
- FastAPI application setup
- CORS middleware configuration
- WebSocket endpoint for real-time chat
- REST API endpoints for HTTP communication
- Health check endpoint

#### ⚙️ **config.py**
- Environment variable management
- Application settings
- Perplexity API configuration

#### 📊 **models.py**
- Pydantic data models for request/response validation
- ChatMessage, ChatResponse, AgricultureQuery models
- Type safety and data validation

#### 🤖 **perplexity_service.py**
- Perplexity AI API integration
- Agricultural domain-specific prompts
- Error handling and fallback responses
- Specialized agricultural conversation flows

#### 🔌 **websocket_manager.py**
- WebSocket connection management
- Real-time message broadcasting
- Client connection tracking
- Connection cleanup

#### 🌾 **agriculture.py**
- Agricultural-specific API endpoints
- Crop management advice
- Market information
- Pest and disease identification
- Weather-based recommendations

### Frontend Components

#### 💬 **ChatInterface.jsx**
- Main chat interface component
- Message state management
- WebSocket and HTTP API integration
- Real-time communication handling
- Loading states and error handling

#### 💭 **MessageBubble.jsx**
- Individual message display component
- User vs bot message styling
- Timestamp formatting
- Message animation

#### ⚡ **QuickActions.jsx**
- Pre-defined quick action buttons
- Agricultural category shortcuts
- User experience enhancement

#### 🔗 **chatService.js**
- API communication service
- WebSocket connection management
- HTTP fallback for API calls
- Specialized agricultural endpoints
- Error handling and retry logic

## 🌟 Key Features Implemented

### 🤖 AI Integration
- **Perplexity AI**: Advanced language model for agricultural expertise
- **Domain-specific prompts**: Tailored for farming and agriculture
- **Fallback responses**: Graceful degradation when AI service is unavailable

### 💬 Real-time Communication
- **WebSocket support**: Instant message delivery
- **HTTP fallback**: Reliable communication when WebSocket fails
- **Connection status**: Visual feedback for users

### 🌾 Agricultural Expertise
- **Crop Management**: Planting, irrigation, fertilization advice
- **Market Information**: Pricing, demand trends, selling opportunities
- **Pest Control**: Identification and treatment recommendations
- **Weather Advice**: Seasonal planning and adaptation strategies
- **Best Practices**: Sustainable farming techniques

### 🎨 User Experience
- **Responsive Design**: Works on all device sizes
- **Quick Actions**: Easy access to common queries
- **Message Animations**: Smooth user interface
- **Loading States**: Clear feedback during processing
- **Error Handling**: Graceful error recovery

### 🔧 Development Features
- **Hot Reload**: Fast development iteration
- **Type Safety**: Pydantic models for data validation
- **API Documentation**: Auto-generated with FastAPI
- **Testing Scripts**: Automated API validation
- **Easy Setup**: One-click installation scripts

## 🚀 Getting Started

1. **Run Setup**: Execute `setup.bat` for automated installation
2. **Configure API**: Add Perplexity API key to `backend/.env`
3. **Start Backend**: `cd backend && python run.py`
4. **Start Frontend**: `cd client && npm run dev`
5. **Test API**: `cd backend && python test_api.py`

## 📚 Documentation

- **API Docs**: http://localhost:8000/docs (when backend is running)
- **README**: Comprehensive setup and usage guide
- **Code Comments**: Detailed inline documentation
- **Type Hints**: Full Python type annotations

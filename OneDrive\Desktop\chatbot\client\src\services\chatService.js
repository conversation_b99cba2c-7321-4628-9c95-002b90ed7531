import axios from 'axios'

class ChatService {
  constructor() {
    this.baseURL = 'http://localhost:8000'
    this.websocket = null
    this.clientId = this.generateClientId()
    this.messageHandlers = []
    this.connectionHandlers = []
    this.connectionStatus = 'disconnected'
    
    // Configure axios
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  generateClientId() {
    return 'client_' + Math.random().toString(36).substr(2, 9)
  }

  connect() {
    try {
      this.updateConnectionStatus('connecting')
      
      const wsURL = `ws://localhost:8000/ws/${this.clientId}`
      this.websocket = new WebSocket(wsURL)
      
      this.websocket.onopen = () => {
        console.log('WebSocket connected')
        this.updateConnectionStatus('connected')
      }
      
      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          this.messageHandlers.forEach(handler => handler(message))
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }
      
      this.websocket.onclose = () => {
        console.log('WebSocket disconnected')
        this.updateConnectionStatus('disconnected')
        
        // Attempt to reconnect after 3 seconds
        setTimeout(() => {
          if (this.connectionStatus === 'disconnected') {
            this.connect()
          }
        }, 3000)
      }
      
      this.websocket.onerror = (error) => {
        console.error('WebSocket error:', error)
        this.updateConnectionStatus('disconnected')
      }
      
    } catch (error) {
      console.error('Error connecting to WebSocket:', error)
      this.updateConnectionStatus('disconnected')
    }
  }

  disconnect() {
    if (this.websocket) {
      this.websocket.close()
      this.websocket = null
    }
    this.updateConnectionStatus('disconnected')
  }

  updateConnectionStatus(status) {
    this.connectionStatus = status
    this.connectionHandlers.forEach(handler => handler(status))
  }

  onMessage(handler) {
    this.messageHandlers.push(handler)
  }

  onConnectionChange(handler) {
    this.connectionHandlers.push(handler)
  }

  async sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      const messageData = {
        message: message,
        timestamp: new Date().toISOString(),
        client_id: this.clientId
      }
      
      this.websocket.send(JSON.stringify(messageData))
    } else {
      throw new Error('WebSocket not connected')
    }
  }

  async sendMessageHTTP(message) {
    try {
      const response = await this.api.post('/api/chat', {
        message: message,
        user_id: this.clientId,
        session_id: this.clientId
      })
      
      return response.data
    } catch (error) {
      console.error('HTTP API error:', error)
      throw error
    }
  }

  // Specialized agriculture endpoints
  async getCropAdvice(query, cropType = null, location = null) {
    try {
      const response = await this.api.post('/api/agriculture/crop-advice', {
        query: query,
        crop_type: cropType,
        location: location,
        category: 'crop_management'
      })
      
      return response.data
    } catch (error) {
      console.error('Crop advice API error:', error)
      throw error
    }
  }

  async getMarketInfo(query, cropType = null, location = null) {
    try {
      const response = await this.api.post('/api/agriculture/market-info', {
        query: query,
        crop_type: cropType,
        location: location,
        category: 'market_prices'
      })
      
      return response.data
    } catch (error) {
      console.error('Market info API error:', error)
      throw error
    }
  }

  async getPestDiseaseHelp(query, cropType = null, location = null) {
    try {
      const response = await this.api.post('/api/agriculture/pest-disease', {
        query: query,
        crop_type: cropType,
        location: location,
        category: 'pest_disease'
      })
      
      return response.data
    } catch (error) {
      console.error('Pest/disease API error:', error)
      throw error
    }
  }

  async getWeatherAdvice(query, cropType = null, location = null) {
    try {
      const response = await this.api.post('/api/agriculture/weather-advice', {
        query: query,
        crop_type: cropType,
        location: location,
        category: 'weather'
      })
      
      return response.data
    } catch (error) {
      console.error('Weather advice API error:', error)
      throw error
    }
  }

  async getAgricultureCategories() {
    try {
      const response = await this.api.get('/api/agriculture/categories')
      return response.data
    } catch (error) {
      console.error('Categories API error:', error)
      throw error
    }
  }

  async checkHealth() {
    try {
      const response = await this.api.get('/health')
      return response.data
    } catch (error) {
      console.error('Health check error:', error)
      throw error
    }
  }
}

// Create and export a singleton instance
export const chatService = new ChatService()
export default ChatService

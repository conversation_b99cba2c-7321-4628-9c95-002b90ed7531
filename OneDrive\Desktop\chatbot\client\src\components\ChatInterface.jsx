import React, { useState, useRef, useEffect } from 'react'
import { Send, Loader2, <PERSON>prout, TrendingUp, Bug, <PERSON>, BookOpen } from 'lucide-react'
import MessageBubble from './MessageBubble'
import QuickActions from './QuickActions'
import { chatService } from '../services/chatService'

const ChatInterface = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! I'm your agricultural assistant. I can help you with crop management, market information, pest control, weather advice, and farming best practices. How can I assist you today?",
      sender: 'bot',
      timestamp: new Date()
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const messagesEndRef = useRef(null)
  const inputRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    // Initialize chat service
    chatService.connect()
    
    // Listen for incoming messages
    chatService.onMessage((message) => {
      setMessages(prev => [...prev, {
        id: Date.now(),
        text: message.response,
        sender: 'bot',
        timestamp: new Date(message.timestamp)
      }])
      setIsLoading(false)
    })

    // Listen for connection status
    chatService.onConnectionChange((status) => {
      setConnectionStatus(status)
    })

    return () => {
      chatService.disconnect()
    }
  }, [])

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage = {
      id: Date.now(),
      text: inputMessage,
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    try {
      if (connectionStatus === 'connected') {
        // Use WebSocket for real-time communication
        await chatService.sendMessage(inputMessage)
      } else {
        // Fallback to REST API
        const response = await chatService.sendMessageHTTP(inputMessage)
        setMessages(prev => [...prev, {
          id: Date.now() + 1,
          text: response.response,
          sender: 'bot',
          timestamp: new Date(response.timestamp)
        }])
        setIsLoading(false)
      }
    } catch (error) {
      console.error('Error sending message:', error)
      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        text: "I'm sorry, I'm having trouble connecting right now. Please try again in a moment.",
        sender: 'bot',
        timestamp: new Date()
      }])
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleQuickAction = (message) => {
    setInputMessage(message)
    inputRef.current?.focus()
  }

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-xl overflow-hidden">
      {/* Connection Status */}
      <div className="bg-green-600 text-white px-4 py-2 text-sm flex items-center justify-between">
        <span className="flex items-center">
          <div className={`w-2 h-2 rounded-full mr-2 ${
            connectionStatus === 'connected' ? 'bg-green-300' : 
            connectionStatus === 'connecting' ? 'bg-yellow-300' : 'bg-red-300'
          }`}></div>
          Agricultural Assistant - {connectionStatus === 'connected' ? 'Online' : 'Offline'}
        </span>
        <span className="text-xs opacity-75">Real-time farming support</span>
      </div>

      {/* Quick Actions */}
      <QuickActions onActionClick={handleQuickAction} />

      {/* Messages Container */}
      <div className="h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 chat-container">
        {messages.map((message) => (
          <MessageBubble key={message.id} message={message} />
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-white rounded-lg p-3 shadow-sm border max-w-xs">
              <div className="flex items-center space-x-2 text-gray-500">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm">Thinking...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t bg-white p-4">
        <div className="flex space-x-2">
          <textarea
            ref={inputRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me about crops, market prices, pest control, weather advice, or farming practices..."
            className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
            rows="2"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>
        
        <div className="mt-2 text-xs text-gray-500 text-center">
          Press Enter to send • Shift+Enter for new line
        </div>
      </div>
    </div>
  )
}

export default ChatInterface

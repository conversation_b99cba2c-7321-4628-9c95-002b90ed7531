from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
import logging

from ..models import AgricultureQuery, ChatResponse
from ..services.perplexity_service import PerplexityService
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/agriculture", tags=["agriculture"])

# Dependency to get Perplexity service
def get_perplexity_service():
    return PerplexityService()

@router.post("/crop-advice", response_model=ChatResponse)
async def get_crop_advice(
    query: AgricultureQuery,
    perplexity_service: PerplexityService = Depends(get_perplexity_service)
):
    """Get crop management advice"""
    try:
        # Enhance query with crop management context
        enhanced_query = f"""
        Crop Management Query: {query.query}
        Crop Type: {query.crop_type or 'Not specified'}
        Location: {query.location or 'Not specified'}
        
        Please provide specific crop management advice including planting, care, and harvesting recommendations.
        """
        
        response = await perplexity_service.get_agricultural_response(enhanced_query)
        
        return ChatResponse(
            response=response,
            timestamp=datetime.now(),
            session_id=str(uuid.uuid4()),
            message_id=str(uuid.uuid4())
        )
    except Exception as e:
        logger.error(f"Error in crop advice endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get crop advice")

@router.post("/market-info", response_model=ChatResponse)
async def get_market_info(
    query: AgricultureQuery,
    perplexity_service: PerplexityService = Depends(get_perplexity_service)
):
    """Get market price and linkage information"""
    try:
        enhanced_query = f"""
        Market Information Query: {query.query}
        Crop Type: {query.crop_type or 'Not specified'}
        Location: {query.location or 'Not specified'}
        
        Please provide current market information, pricing trends, and selling opportunities for farmers.
        Include information about market linkages and best practices for selling crops.
        """
        
        response = await perplexity_service.get_agricultural_response(enhanced_query)
        
        return ChatResponse(
            response=response,
            timestamp=datetime.now(),
            session_id=str(uuid.uuid4()),
            message_id=str(uuid.uuid4())
        )
    except Exception as e:
        logger.error(f"Error in market info endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get market information")

@router.post("/pest-disease", response_model=ChatResponse)
async def get_pest_disease_help(
    query: AgricultureQuery,
    perplexity_service: PerplexityService = Depends(get_perplexity_service)
):
    """Get pest and disease identification and treatment advice"""
    try:
        enhanced_query = f"""
        Pest/Disease Query: {query.query}
        Crop Type: {query.crop_type or 'Not specified'}
        Location: {query.location or 'Not specified'}
        
        Please help identify the pest or disease and provide treatment recommendations.
        Include organic and chemical treatment options with safety precautions.
        """
        
        response = await perplexity_service.get_agricultural_response(enhanced_query)
        
        return ChatResponse(
            response=response,
            timestamp=datetime.now(),
            session_id=str(uuid.uuid4()),
            message_id=str(uuid.uuid4())
        )
    except Exception as e:
        logger.error(f"Error in pest/disease endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get pest/disease help")

@router.post("/weather-advice", response_model=ChatResponse)
async def get_weather_advice(
    query: AgricultureQuery,
    perplexity_service: PerplexityService = Depends(get_perplexity_service)
):
    """Get weather-based farming recommendations"""
    try:
        enhanced_query = f"""
        Weather-based Farming Query: {query.query}
        Crop Type: {query.crop_type or 'Not specified'}
        Location: {query.location or 'Not specified'}
        
        Please provide weather-based farming advice, seasonal planning recommendations,
        and suggestions for adapting to current weather conditions.
        """
        
        response = await perplexity_service.get_agricultural_response(enhanced_query)
        
        return ChatResponse(
            response=response,
            timestamp=datetime.now(),
            session_id=str(uuid.uuid4()),
            message_id=str(uuid.uuid4())
        )
    except Exception as e:
        logger.error(f"Error in weather advice endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get weather advice")

@router.get("/categories")
async def get_agriculture_categories():
    """Get available agriculture advice categories"""
    return {
        "categories": [
            {
                "id": "crop_management",
                "name": "Crop Management",
                "description": "Planting, irrigation, fertilization, and harvesting advice"
            },
            {
                "id": "market_prices",
                "name": "Market Information",
                "description": "Current prices, demand trends, and selling opportunities"
            },
            {
                "id": "pest_disease",
                "name": "Pest & Disease Control",
                "description": "Identification and treatment of plant diseases and pests"
            },
            {
                "id": "weather",
                "name": "Weather-based Advice",
                "description": "Seasonal planning and weather adaptation strategies"
            },
            {
                "id": "best_practices",
                "name": "Best Practices",
                "description": "Sustainable farming techniques and modern methods"
            }
        ]
    }

import React from 'react'
import { User, Bot } from 'lucide-react'

const MessageBubble = ({ message }) => {
  const isBot = message.sender === 'bot'
  
  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const formatMessage = (text) => {
    // Simple formatting for better readability
    return text.split('\n').map((line, index) => (
      <React.Fragment key={index}>
        {line}
        {index < text.split('\n').length - 1 && <br />}
      </React.Fragment>
    ))
  }

  return (
    <div className={`flex message-bubble ${isBot ? 'justify-start' : 'justify-end'}`}>
      <div className={`flex max-w-xs lg:max-w-md ${isBot ? 'flex-row' : 'flex-row-reverse'}`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 ${isBot ? 'mr-3' : 'ml-3'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            isBot 
              ? 'bg-green-100 text-green-600' 
              : 'bg-blue-100 text-blue-600'
          }`}>
            {isBot ? <Bot className="w-5 h-5" /> : <User className="w-5 h-5" />}
          </div>
        </div>

        {/* Message Content */}
        <div className={`rounded-lg p-3 shadow-sm ${
          isBot 
            ? 'bg-white border border-gray-200' 
            : 'bg-green-600 text-white'
        }`}>
          <div className={`text-sm ${isBot ? 'text-gray-800' : 'text-white'}`}>
            {formatMessage(message.text)}
          </div>
          
          {/* Timestamp */}
          <div className={`text-xs mt-1 ${
            isBot ? 'text-gray-500' : 'text-green-100'
          }`}>
            {formatTime(message.timestamp)}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MessageBubble

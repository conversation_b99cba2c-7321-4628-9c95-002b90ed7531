#!/usr/bin/env python3
"""
Test script for Agricultural Chatbot API
Run this to validate the backend functionality
"""

import asyncio
import httpx
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

async def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Health check passed: {data['status']}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Health check error: {str(e)}")
        return False

async def test_chat_endpoint():
    """Test the general chat endpoint"""
    print("\n💬 Testing chat endpoint...")
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            payload = {
                "message": "Hello, I need help with tomato farming",
                "user_id": "test_user",
                "session_id": "test_session"
            }
            response = await client.post(f"{BASE_URL}/api/chat", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Chat endpoint working")
                print(f"📝 Response preview: {data['response'][:100]}...")
                return True
            else:
                print(f"❌ Chat endpoint failed: {response.status_code}")
                print(f"Error: {response.text}")
                return False
    except Exception as e:
        print(f"❌ Chat endpoint error: {str(e)}")
        return False

async def test_agriculture_endpoints():
    """Test agricultural specific endpoints"""
    print("\n🌾 Testing agriculture endpoints...")
    
    endpoints = [
        ("crop-advice", "How do I plant tomatoes?"),
        ("market-info", "What are current tomato prices?"),
        ("pest-disease", "My tomato leaves have spots"),
        ("weather-advice", "How does rain affect my crops?")
    ]
    
    results = []
    
    for endpoint, query in endpoints:
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                payload = {
                    "query": query,
                    "crop_type": "tomato",
                    "location": "test_location"
                }
                response = await client.post(
                    f"{BASE_URL}/api/agriculture/{endpoint}", 
                    json=payload
                )
                
                if response.status_code == 200:
                    print(f"✅ {endpoint} endpoint working")
                    results.append(True)
                else:
                    print(f"❌ {endpoint} endpoint failed: {response.status_code}")
                    results.append(False)
                    
        except Exception as e:
            print(f"❌ {endpoint} endpoint error: {str(e)}")
            results.append(False)
    
    return all(results)

async def test_categories_endpoint():
    """Test the categories endpoint"""
    print("\n📋 Testing categories endpoint...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/api/agriculture/categories")
            
            if response.status_code == 200:
                data = response.json()
                categories = data.get('categories', [])
                print(f"✅ Categories endpoint working")
                print(f"📊 Found {len(categories)} categories")
                for cat in categories:
                    print(f"   - {cat['name']}: {cat['description']}")
                return True
            else:
                print(f"❌ Categories endpoint failed: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Categories endpoint error: {str(e)}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting Agricultural Chatbot API Tests")
    print("=" * 50)
    
    tests = [
        test_health_check(),
        test_categories_endpoint(),
        test_chat_endpoint(),
        test_agriculture_endpoints()
    ]
    
    results = await asyncio.gather(*tests, return_exceptions=True)
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = sum(1 for result in results if result is True)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Your Agricultural Chatbot API is ready!")
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        print("\nTroubleshooting tips:")
        print("1. Make sure the backend server is running (python run.py)")
        print("2. Check if Perplexity API key is set in .env file")
        print("3. Verify all dependencies are installed")
    
    print("\n🌐 API Documentation: http://localhost:8000/docs")

if __name__ == "__main__":
    asyncio.run(main())

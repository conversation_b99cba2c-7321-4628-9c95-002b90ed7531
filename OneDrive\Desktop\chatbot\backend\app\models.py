from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class ChatMessage(BaseModel):
    message: str
    user_id: Optional[str] = "anonymous"
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    timestamp: datetime
    session_id: str
    message_id: str

class AgricultureQuery(BaseModel):
    query: str
    category: Optional[str] = None  # crop_management, market_prices, weather, pest_disease, best_practices
    location: Optional[str] = None
    crop_type: Optional[str] = None

class HealthCheck(BaseModel):
    status: str
    timestamp: datetime
    version: str = "1.0.0"

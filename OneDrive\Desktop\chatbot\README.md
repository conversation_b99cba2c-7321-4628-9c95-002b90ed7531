# 🌾 Agricultural Chatbot Application

An AI-driven agricultural assistance chatbot that provides real-time support for farmers in cultivation practices and market linkages to address challenges related to crop management, income optimization, and food security.

## 🚀 Features

- **Real-time Chat Interface**: WebSocket-based communication for instant responses
- **Agricultural Domain Expertise**: Specialized AI responses for farming topics
- **Crop Management Advice**: Planting, irrigation, fertilization, and harvesting guidance
- **Market Information**: Current prices, demand trends, and selling opportunities
- **Pest & Disease Control**: Identification and treatment recommendations
- **Weather-based Recommendations**: Seasonal planning and weather adaptation
- **Sustainable Practices**: Environmentally friendly farming techniques
- **Mobile-responsive Design**: Works on all devices

## 🛠 Technology Stack

### Backend
- **FastAPI**: Modern Python web framework
- **Perplexity AI**: Advanced language model for agricultural expertise
- **WebSockets**: Real-time communication
- **Uvicorn**: ASGI server

### Frontend
- **React 19**: Modern UI library
- **Vite**: Fast build tool
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Beautiful icons
- **Axios**: HTTP client

## 📋 Prerequisites

- Python 3.8+
- Node.js 16+
- npm or yarn
- Perplexity AI API key

## 🔧 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd chatbot
```

### 2. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
```

### 3. Configure Environment Variables

Edit the `.env` file in the backend directory:

```env
PERPLEXITY_API_KEY=your_perplexity_api_key_here
ENVIRONMENT=development
HOST=localhost
PORT=8000
CORS_ORIGINS=http://localhost:5173,http://localhost:3000
```

### 4. Frontend Setup

```bash
# Navigate to frontend directory
cd ../client

# Install dependencies
npm install
```

## 🚀 Running the Application

### Start the Backend Server

```bash
cd backend
python run.py
```

The backend will be available at: `http://localhost:8000`

API Documentation: `http://localhost:8000/docs`

### Start the Frontend Development Server

```bash
cd client
npm run dev
```

The frontend will be available at: `http://localhost:5173`

## 📡 API Endpoints

### General Chat
- `POST /api/chat` - General chat endpoint
- `GET /health` - Health check

### Agricultural Specific
- `POST /api/agriculture/crop-advice` - Crop management advice
- `POST /api/agriculture/market-info` - Market information
- `POST /api/agriculture/pest-disease` - Pest and disease help
- `POST /api/agriculture/weather-advice` - Weather-based recommendations
- `GET /api/agriculture/categories` - Available categories

### WebSocket
- `WS /ws/{client_id}` - Real-time chat communication

## 🌱 Usage Examples

### Crop Management
- "How do I prepare soil for tomato planting?"
- "What's the best irrigation schedule for wheat?"
- "When should I harvest my corn crop?"

### Market Information
- "What are current rice prices in my area?"
- "Where can I sell my vegetables for the best price?"
- "What crops are in high demand this season?"

### Pest Control
- "My tomato leaves have yellow spots, what could it be?"
- "How do I treat aphids on my crops naturally?"
- "What are signs of fungal diseases in wheat?"

### Weather Advice
- "How should I prepare my crops for the upcoming rain?"
- "What crops are suitable for dry season planting?"
- "How does temperature affect crop growth?"

## 🔧 Development

### Backend Development
```bash
cd backend
uvicorn app.main:app --reload --host localhost --port 8000
```

### Frontend Development
```bash
cd client
npm run dev
```

### Building for Production
```bash
# Frontend
cd client
npm run build

# Backend
cd backend
pip install -r requirements.txt
python run.py
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Review the troubleshooting section below
- Contact the development team

## 🔍 Troubleshooting

### Common Issues

1. **Backend won't start**
   - Check if Python virtual environment is activated
   - Verify all dependencies are installed
   - Ensure Perplexity API key is set correctly

2. **Frontend can't connect to backend**
   - Verify backend is running on port 8000
   - Check CORS settings in backend configuration
   - Ensure no firewall is blocking the connection

3. **WebSocket connection fails**
   - Check if backend WebSocket endpoint is accessible
   - Verify client ID generation
   - Check browser console for error messages

4. **AI responses are slow or failing**
   - Verify Perplexity API key is valid
   - Check internet connection
   - Review API rate limits

## 🎯 Future Enhancements

- User authentication and profiles
- Chat history persistence
- Image upload for crop/pest identification
- Integration with weather APIs
- Multi-language support
- Offline mode capabilities
- Mobile app development

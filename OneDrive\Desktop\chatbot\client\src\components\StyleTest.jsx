import React from 'react'
import { Sprout, TrendingUp, Bug, Cloud, BookOpen } from 'lucide-react'

const StyleTest = () => {
  return (
    <div className="p-8 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold text-green-800 mb-8">Tailwind CSS Style Test</h1>
      
      {/* Test basic Tailwind classes */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Basic Styling Test</h2>
        <div className="bg-white p-4 rounded-lg shadow-md border">
          <p className="text-gray-700">This should have white background, padding, rounded corners, and shadow.</p>
        </div>
      </div>

      {/* Test QuickActions styling */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">QuickActions Component Styling</h2>
        <div className="border-b bg-gray-50 p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h3>
          <div className="flex flex-wrap gap-2">
            {[
              { icon: Sprout, label: 'Crop Management' },
              { icon: TrendingUp, label: 'Market Prices' },
              { icon: Bug, label: 'Pest Control' },
              { icon: Cloud, label: 'Weather Advice' },
              { icon: BookOpen, label: 'Best Practices' }
            ].map((action, index) => {
              const IconComponent = action.icon
              return (
                <button
                  key={index}
                  className="flex items-center space-x-2 bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm text-gray-700 hover:bg-green-50 hover:border-green-200 hover:text-green-700 transition-colors duration-200"
                >
                  <IconComponent className="w-4 h-4" />
                  <span>{action.label}</span>
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* Test responsive design */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Responsive Design Test</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-blue-100 p-4 rounded">Mobile: 1 column</div>
          <div className="bg-green-100 p-4 rounded">Tablet: 2 columns</div>
          <div className="bg-yellow-100 p-4 rounded">Desktop: 3 columns</div>
        </div>
      </div>

      {/* Test hover effects */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Hover Effects Test</h2>
        <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200 mr-4">
          Hover me (Green)
        </button>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
          Hover me (Blue)
        </button>
      </div>

      {/* Test icons */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Icons Test</h2>
        <div className="flex space-x-4">
          <Sprout className="w-6 h-6 text-green-600" />
          <TrendingUp className="w-6 h-6 text-blue-600" />
          <Bug className="w-6 h-6 text-red-600" />
          <Cloud className="w-6 h-6 text-gray-600" />
          <BookOpen className="w-6 h-6 text-purple-600" />
        </div>
      </div>
    </div>
  )
}

export default StyleTest

import React from 'react'
import ChatInterface from './components/ChatInterface'
import StyleTest from './components/StyleTest'
import './App.css'

function App() {
  const [showStyleTest, setShowStyleTest] = React.useState(false)

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-green-800 mb-2">
            🌾 Agricultural Assistant
          </h1>
          <p className="text-lg text-gray-600">
            Your AI-powered farming companion for crop management, market insights, and agricultural guidance
          </p>
          <button
            onClick={() => setShowStyleTest(!showStyleTest)}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            {showStyleTest ? 'Show Chat Interface' : 'Show Style Test'}
          </button>
        </header>

        {showStyleTest ? <StyleTest /> : <ChatInterface />}
      </div>
    </div>
  )
}

export default App

import os
from dotenv import load_dotenv
from pydantic_settings import BaseSettings

load_dotenv()

class Settings(BaseSettings):
    perplexity_api_key: str = os.getenv("PERPLEXITY_API_KEY", "")
    environment: str = os.getenv("ENVIRONMENT", "development")
    host: str = os.getenv("HOST", "localhost")
    port: int = int(os.getenv("PORT", "8000"))
    cors_origins: list = os.getenv("CORS_ORIGINS", "http://localhost:5173").split(",")
    
    class Config:
        env_file = ".env"

settings = Settings()

import React from 'react'
import { <PERSON>prout, TrendingUp, Bug, Cloud, BookOpen } from 'lucide-react'

const QuickActions = ({ onActionClick }) => {
  const quickActions = [
    {
      icon: Sprout,
      label: 'Crop Management',
      message: 'I need advice on crop management and cultivation practices'
    },
    {
      icon: TrendingUp,
      label: 'Market Prices',
      message: 'What are the current market prices and selling opportunities?'
    },
    {
      icon: Bug,
      label: 'Pest Control',
      message: 'Help me identify and treat pests or diseases in my crops'
    },
    {
      icon: Cloud,
      label: 'Weather Advice',
      message: 'Give me weather-based farming recommendations'
    },
    {
      icon: BookOpen,
      label: 'Best Practices',
      message: 'Share sustainable farming techniques and best practices'
    }
  ]

  return (
    <div className="border-b bg-gray-50 p-4 te">
      <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h3>
      <div className="flex flex-wrap gap-2">
        {quickActions.map((action, index) => {
          const IconComponent = action.icon
          return (
            <button
              key={index}
              onClick={() => onActionClick(action.message)}
              className="flex items-center space-x-2 bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm text-gray-700 hover:bg-green-50 hover:border-green-200 hover:text-green-700 transition-colors duration-200"
            >
              <IconComponent className="w-4 h-4" />
              <span>{action.label}</span>
            </button>
          )
        })}
      </div>
    </div>
  )
}

export default QuickActions

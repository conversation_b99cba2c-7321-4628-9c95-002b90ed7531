from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from datetime import datetime
import json
import uuid
import logging

from .config import settings
from .models import ChatMessage, ChatResponse, AgricultureQuery, HealthCheck
from .services.perplexity_service import PerplexityService
from .services.websocket_manager import WebSocketManager
from .routers import agriculture

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Agricultural Chatbot API",
    description="AI-driven agricultural assistance chatbot for farmers",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
perplexity_service = PerplexityService()
websocket_manager = WebSocketManager()

# Include routers
app.include_router(agriculture.router)

@app.get("/")
async def root():
    return {"message": "Agricultural Chatbot API is running"}

@app.get("/health", response_model=HealthCheck)
async def health_check():
    return HealthCheck(
        status="healthy",
        timestamp=datetime.now()
    )

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(message: ChatMessage):
    try:
        # Generate response using Perplexity AI
        response = await perplexity_service.get_agricultural_response(
            message.message,
            user_id=message.user_id
        )
        
        # Create response object
        chat_response = ChatResponse(
            response=response,
            timestamp=datetime.now(),
            session_id=message.session_id or str(uuid.uuid4()),
            message_id=str(uuid.uuid4())
        )
        
        return chat_response
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await websocket_manager.connect(websocket, client_id)
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Process message
            response = await perplexity_service.get_agricultural_response(
                message_data.get("message", ""),
                user_id=client_id
            )
            
            # Send response back to client
            response_data = {
                "response": response,
                "timestamp": datetime.now().isoformat(),
                "message_id": str(uuid.uuid4())
            }
            
            await websocket_manager.send_personal_message(
                json.dumps(response_data), 
                client_id
            )
            
    except WebSocketDisconnect:
        websocket_manager.disconnect(client_id)
        logger.info(f"Client {client_id} disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        websocket_manager.disconnect(client_id)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=True if settings.environment == "development" else False
    )
